import React, { useEffect, useState } from 'react';
import styles from 'Css/profileList.css';
import btnStyles from 'Css/button.css';
import Modal2 from 'components/Common/Modal2';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import saga from 'containers/Common/SubComponents/SousZoneForm/saga';
import reducer from 'containers/Common/SubComponents/SousZoneForm/reducer';
import sagaFich from "containers/EspaceAssistant/FicheAssistant/saga"
import reducerFich from "containers/EspaceAssistant/FicheAssistant/reducer"
import { useParams } from 'react-router-dom';
import {
  deleteSousZone,
  resetAddSousZone,
  resetDeleteSousZone,
} from 'containers/Common/SubComponents/SousZoneForm/actions'; 
import CustomPagination from '../../../Common/CustomPagination';


import {
  makeSelectSousZone,
  makeSelectSousZoneDeleteSuccess,
  makeSelectError,
  makeSelectSuccess, 
} from 'containers/Common/SubComponents/SousZoneForm/selectors';
import {
  makeSelectZonesWithSousZones,
} from 'containers/EspaceAssistant/FicheAssistant/selectors'
import { loadSousZones,loadZonesWithSousZones } from 'containers/EspaceAssistant/FicheAssistant/actions';
import SousZoneForm from 'containers/Common/SubComponents/SousZoneForm';
import {
  DELETE_ICON,
  EDIT_ICON,
} from '../../../Common/ListIcons/ListIcons';
import plus from 'images/icons/plus.svg';
import minus from 'images/icons/minus.svg';
import tagStyles from 'Css/tag.css';
import { Alert } from 'react-bootstrap';

const key = 'sousZone';

const emptyValue = <span>-</span>;

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  error: makeSelectError,
  sousZone: makeSelectSousZone,
  successDelete: makeSelectSousZoneDeleteSuccess, 
  zonesWithSousZones: makeSelectZonesWithSousZones()
});




export default function SousZones(props) {
  const dispatch = useDispatch();
  const zoneId = props.zoneId;
  const assistantId=props.assistantId;
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  useInjectReducer({ key: 'assistantFiche', reducer: reducerFich });
  useInjectSaga({ key: 'assistantFiche', saga: sagaFich });

  const params = useParams();
  const formatDate = date => moment(date).format('DD/MM/YYYY');

  const { success, error, sousZone, successDelete,zonesWithSousZones } = useSelector(omdbSelector);
  const [sousZoneToEdit, setSousZoneToEdit] = useState('');
  const [message, setMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [show, setShow] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [sousZoneToDelete, setSousZoneToDelete] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [expandedZones, setExpandedZones] = useState({});

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;
  const sousZones = props.sousZones;
  const handleClose = () => setShow(false);

  useEffect(() => {
    if (successDelete) { 
      setShowAlert(true);
      dispatch(loadSousZones(zoneId));
      // dispatch(loadZonesWithSousZones(assistantId));
      setMessage('Sous Zone supprimée avec succès');
      dispatch(resetDeleteSousZone());
     dispatch(loadZonesWithSousZones(assistantId));

    }
  }, [successDelete, dispatch, sousZone, params.id]);

  // add teh error check here
  useEffect(() => {
    if (error)
    {
      // the error should inclue this "his SousZone is atta"
      if (error.includes('It cannot be deleted')) {
        setShowErrorAlert(true);
        setErrorMessage(
          'Cette Sous Zone est attachée à un ou plusieurs bénéficiaires et ne peut pas être supprimée',
        );
      }
      dispatch(resetDeleteSousZone());
    }
  }, [error, dispatch, sousZone, params.id]);

  useEffect(() => {
    if (success) {
      setShowAlert(true);
      setMessage(
        sousZoneToEdit
          ? 'Sous Zone modifiée avec succès'
          : 'Sous Zone ajoutée avec succès',
      );
      setSousZoneToEdit('');
      dispatch(loadSousZones(zoneId));
      dispatch(resetAddSousZone());
    dispatch(loadZonesWithSousZones(assistantId));

    }
  }, [success, dispatch, sousZoneToEdit, sousZone, params.id]);

  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => setShowAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if (showErrorAlert) {
      const timer = setTimeout(() => setShowErrorAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showErrorAlert]);

  useEffect(() => {
    dispatch(loadSousZones(zoneId));
    dispatch(loadZonesWithSousZones(assistantId));
  }, [dispatch, zoneId,assistantId]);

  console.log({ sousZones });

  const toggleZoneExpansion = zoneId => {
    setExpandedZones(prev => ({
      ...prev,
      [zoneId]: !prev[zoneId],
    }));
  };

  const editSousZoneHandler = sousZone => {
    setSousZoneToEdit(sousZone);
    setShow(true);
  };

  const deleteSousZoneHandler = sousZone => {
    setSousZoneToDelete(sousZone);
    setShowDeleteModal(true);
  };

  let listZones = [];
  
  if (zonesWithSousZones && Array.isArray(zonesWithSousZones)) {
    // Create a shallow copy of the array before sorting it
    const sortedZones = [...zonesWithSousZones].sort((a, b) => b.id - a.id);

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, sortedZones.length);
    const paginatedZones = sortedZones.slice(startIndex, endIndex);

    listZones = paginatedZones.map(zone => ({
      id: zone.id,
      name: zone.name || emptyValue,
      nameAr: zone.nameAr || emptyValue,
      code: zone.code || emptyValue,
      dateAffectation: zone.dateAffectation ? formatDate(zone.dateAffectation) : emptyValue,
      dateFinAffectation: zone.dateFinAffectation ? formatDate(zone.dateFinAffectation) : emptyValue,
      zoneDTOList: zone.zoneDTOList || [],
    }));
  }



  return (
    <div>
      {showAlert && (
        <Alert
          className="alert-style"
          variant="success"
          onClose={() => setShowAlert(false)}
          dismissible
        >
          <p>{message}</p>
        </Alert>
      )}
      {showErrorAlert && (
        <Alert
          className="alert-style"
          variant="danger"
          onClose={() => setShowErrorAlert(false)}
          dismissible
        >
          <p>{errorMessage}</p>
        </Alert>
      )}
      <div className={styles.backgroudStyle}>
        <div className={styles.global}>
          <div className={styles.header}>
            <h4>Liste des Zones</h4>
            <button
              className={btnStyles.addBtnProfile}
              onClick={() => {
                setShow(true);
                setSousZoneToEdit('');
              }}
            >
              Ajouter
            </button>
          </div>
          <div className={styles.content}>
            <table className="table small">
              <thead>
                <tr>
                  <th scope="col"></th>
                  <th style={{ whiteSpace: 'nowrap' }}>Nom</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Nom Arabe</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Code</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Date d'affectation</th>
                  <th style={{ whiteSpace: 'nowrap' }}>Date fin d'affectation</th>
                   
                </tr>
              </thead>
              <tbody>
                {listZones.map(zone => (
                  <React.Fragment key={zone.id}>
                    <tr>
                      <td className="col-md-1">
                        {zone.zoneDTOList && zone.zoneDTOList.length > 0 ? (
                          <button
                            type="button"
                            onClick={() => toggleZoneExpansion(zone.id)}
                            className="bg-transparent"
                          >
                            <img
                              src={expandedZones[zone.id] ? minus : plus}
                              width="20px"
                              height="20px"
                              className=""
                              data-dismiss="modal"
                              alt={expandedZones[zone.id] ? "minus" : "plus"}
                            />
                          </button>
                        ) : null}
                      </td>
                      <td className="col-md-1">{zone.name}</td>
                      <td className="col-md-1">{zone.nameAr}</td>
                      <td className="col-md-1">{zone.code}</td>
                      <td className="col-md-1">{zone.dateAffectation}</td>
                      <td className="col-md-1">{zone.dateFinAffectation}</td>
                     
                    </tr>

                    {/* Sous-zones Rows */}
                    {zone.zoneDTOList && zone.zoneDTOList.length > 0 && expandedZones[zone.id] && (
                      <tr>
                        <td colSpan={7} className="border-top-0" style={{ padding: '20px', display: 'flex', justifyContent: 'center' }}>
                          <div style={{
                            backgroundColor: '#fffbf0',
                            borderRadius: '12px',
                            border: '2px solid #f0e68c',
                            overflow: 'hidden',
                            boxShadow: '0 4px 12px rgba(240, 230, 140, 0.3)',
                            width: '70%',
                            minWidth: '600px'
                          }}>
                            <table className="table table-sm mb-0" style={{ margin: '0', fontSize: '0.75rem' }}>
                              <thead>
                                <tr style={{
                                  backgroundColor: '#fff8dc',
                                  borderBottom: '2px solid #f0e68c'
                                }}>
                                  <th style={{
                                    fontWeight: '700',
                                    fontSize: '0.7rem',
                                    color: '#8b7355',
                                    padding: '8px 10px',
                                    border: 'none',
                                    borderRight: '1px solid #f0e68c',
                                    textAlign: 'center'
                                  }}>
                                    Code Sous-Zone
                                  </th>
                                  <th style={{
                                    fontWeight: '700',
                                    fontSize: '0.7rem',
                                    color: '#8b7355',
                                    padding: '8px 10px',
                                    border: 'none',
                                    borderRight: '1px solid #f0e68c',
                                    textAlign: 'center'
                                  }}>
                                    Nom
                                  </th>
                                  <th style={{
                                    fontWeight: '700',
                                    fontSize: '0.7rem',
                                    color: '#8b7355',
                                    padding: '8px 10px',
                                    border: 'none',
                                    borderRight: '1px solid #f0e68c',
                                    textAlign: 'center'
                                  }}>
                                    Nom Arabe
                                  </th>
                                  <th style={{
                                    fontWeight: '700',
                                    fontSize: '0.7rem',
                                    color: '#8b7355',
                                    padding: '8px 10px',
                                    border: 'none',
                                    borderRight: '1px solid #f0e68c',
                                    textAlign: 'center'
                                  }}>
                                    Détail
                                  </th>
                                  <th style={{
                                    fontWeight: '700',
                                    fontSize: '0.7rem',
                                    color: '#8b7355',
                                    padding: '8px 10px',
                                    border: 'none',
                                    textAlign: 'center'
                                  }}>
                                    Actions
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {zone.zoneDTOList && zone.zoneDTOList.length > 0 ? (
                                  zone.zoneDTOList.map((sousZone) => (
                                  <tr
                                    key={sousZone.id}
                                    style={{
                                      backgroundColor: '#fffbf0',
                                      borderBottom: '1px solid #f0e68c'
                                    }}
                                  >
                                    <td style={{
                                      padding: '6px 10px',
                                      fontSize: '0.7rem',
                                      color: '#8b7355',
                                      border: 'none',
                                      borderRight: '1px solid #f0e68c',
                                      textAlign: 'center'
                                    }}>
                                      {sousZone.code || emptyValue}
                                    </td>
                                    <td style={{
                                      padding: '6px 10px',
                                      fontSize: '0.7rem',
                                      color: '#8b7355',
                                      border: 'none',
                                      borderRight: '1px solid #f0e68c',
                                      textAlign: 'center'
                                    }}>
                                      {sousZone.name || emptyValue}
                                    </td>
                                    <td style={{
                                      padding: '6px 10px',
                                      fontSize: '0.7rem',
                                      color: '#8b7355',
                                      border: 'none',
                                      borderRight: '1px solid #f0e68c',
                                      textAlign: 'center'
                                    }}>
                                      {sousZone.nameAr || emptyValue}
                                    </td>
                                    <td style={{
                                      padding: '6px 10px',
                                      fontSize: '0.7rem',
                                      color: '#8b7355',
                                      border: 'none',
                                      borderRight: '1px solid #f0e68c',
                                      textAlign: 'center'
                                    }}>
                                      {sousZone.detail || emptyValue}
                                    </td>
                                    <td style={{
                                      padding: '6px 10px',
                                      border: 'none',
                                      textAlign: 'center'
                                    }}>
                                      <input
                                        type="image"
                                        src={EDIT_ICON}
                                        width="28px"
                                        height="28px"
                                        className="p-1"
                                        title="Modifier Sous-Zone"
                                        onClick={() => editSousZoneHandler(sousZone)}
                                        style={{
                                          marginRight: '10px',
                                          cursor: 'pointer',
                                          opacity: '0.8',
                                          transition: 'all 0.2s ease',
                                          filter: 'drop-shadow(0 2px 4px rgba(139, 115, 85, 0.3))'
                                        }}
                                        onMouseEnter={(e) => {
                                          e.target.style.opacity = '1';
                                          e.target.style.transform = 'scale(1.1)';
                                        }}
                                        onMouseLeave={(e) => {
                                          e.target.style.opacity = '0.8';
                                          e.target.style.transform = 'scale(1)';
                                        }}
                                      />
                                      <input
                                        type="image"
                                        src={DELETE_ICON}
                                        width="28px"
                                        height="28px"
                                        className="p-1"
                                        title="Supprimer Sous-Zone"
                                        onClick={() => deleteSousZoneHandler(sousZone)}
                                        style={{
                                          cursor: 'pointer',
                                          opacity: '0.8',
                                          transition: 'all 0.2s ease',
                                          filter: 'drop-shadow(0 2px 4px rgba(139, 115, 85, 0.3))'
                                        }}
                                        onMouseEnter={(e) => {
                                          e.target.style.opacity = '1';
                                          e.target.style.transform = 'scale(1.1)';
                                        }}
                                        onMouseLeave={(e) => {
                                          e.target.style.opacity = '0.8';
                                          e.target.style.transform = 'scale(1)';
                                        }}
                                      />
                                    </td>
                                  </tr>
                                  ))
                                ) : (
                                  <tr>
                                    <td colSpan={5} style={{
                                      padding: '16px',
                                      fontStyle: 'italic',
                                      color: '#8b7355',
                                      textAlign: 'center',
                                      border: 'none',
                                      fontSize: '0.75rem'
                                    }}>
                                      Aucune sous-zone disponible
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>

          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <CustomPagination
              totalCount={Math.ceil(
                (zonesWithSousZones && Array.isArray(zonesWithSousZones) ? zonesWithSousZones.length : 0) / pageSize,
              )}
              pageSize={pageSize}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              // should we pass the handleClose function here?
              handleClose={handleClose}
            />
          </div>
        </div>
      </div>

      <Modal2
        title={
          sousZoneToEdit ? 'Modifier la Sous Zone' : 'Ajouter une Sous Zone'
        }
        show={show}
        handleClose={handleClose}
      >
        <SousZoneForm
          zoneId={sousZoneToEdit ? zoneId : null}
          handleClose={handleClose}
          sousZoneToEdit={sousZoneToEdit}
          availableZones={zonesWithSousZones}
        />
      </Modal2>

      <Modal2
        title="Confirmation de suppression"
        show={showDeleteModal}
        handleClose={() => setShowDeleteModal(false)}
      >
        <p>Êtes-vous sûr de vouloir supprimer cette Sous Zone ?</p>
        <div>
          <button
            className={btnStyles.cancelBtn}
            onClick={() => setShowDeleteModal(false)}
          >
            Annuler
          </button>
          <button
            className={btnStyles.deleteBtn}
            onClick={() => {
              dispatch(deleteSousZone(sousZoneToDelete.id));
              setShowDeleteModal(false);
            }}
          >
            Supprimer
          </button>
        </div>
      </Modal2>
    </div>
  );
}
